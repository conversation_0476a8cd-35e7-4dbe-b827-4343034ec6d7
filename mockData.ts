import { Visit, Doctor } from './types';

export const doctors: Doctor[] = [
  { id: 'doc1', name: 'Dr. <PERSON>' },
  { id: 'doc2', name: 'Dr. <PERSON>' },
  { id: 'doc3', name: 'Dr. <PERSON>' },
];

const complaints = ['Cough', 'Fever', 'Headache', 'Abdominal Pain', 'Fatigue', 'Dizziness', 'Shortness of breath', 'Chest pain', 'Nausea', 'Back pain'];
const diagnoses = ['URTI', 'Hypertension', 'Diabetes', 'Migraine', 'Anemia', 'Gastroenteritis', 'Bronchitis', 'Allergic Rhinitis'];

const generateRandomDate = (start: Date, end: Date): string => {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime())).toISOString();
};

export const visits: Visit[] = Array.from({ length: 500 }, (_, i) => {
  const doctor = doctors[i % doctors.length];
  const diagnosis = diagnoses[i % diagnoses.length];
  const age = Math.floor(Math.random() * 80) + 5;
  const gender = Math.random() > 0.5 ? 'Female' : 'Male';
  const complaint = complaints[Math.floor(Math.random() * complaints.length)];

  let relatedComplaint = complaint;
  if(diagnosis === 'URTI' || diagnosis === 'Bronchitis') relatedComplaint = ['Cough', 'Fever', 'Shortness of breath'][Math.floor(Math.random()*3)];
  if(diagnosis === 'Hypertension' || diagnosis === 'Migraine') relatedComplaint = ['Headache', 'Dizziness', 'Fatigue'][Math.floor(Math.random()*3)];
  if(diagnosis === 'Gastroenteritis') relatedComplaint = ['Abdominal Pain', 'Nausea'][Math.floor(Math.random()*2)];

  return {
    id: `visit${i + 1}`,
    patient: {
      id: `pat${i + 1}`,
      age,
      gender,
    },
    doctor,
    date: generateRandomDate(new Date(2023, 0, 1), new Date(2023, 11, 31)),
    chiefComplaint: relatedComplaint,
    diagnosis,
    medications: ['Paracetamol', 'Amoxicillin', 'Lisinopril', 'Metformin', 'Sumatriptan', 'Ferrous Sulfate', 'Atorvastatin', 'Omeprazole'].slice(0, Math.floor(Math.random() * 6) + 1),
    investigations: ['CBC', 'Chest X-ray', 'Blood Sugar', 'ECG'].slice(0, Math.floor(Math.random() * 2)),
    referred: Math.random() > 0.85,
  };
});

export const chiefComplaintsForAI: string[] = Array.from(new Set(visits.map(v => v.chiefComplaint).slice(0, 50)));