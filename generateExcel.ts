import * as XLSX from 'xlsx';
import { doctors, visits } from './mockData';
import {
  topDiagnosesByComplaintData,
  complaintToDiagnosisData,
  symptomProgressionData,
  treatmentEfficacyData,
  diagnosisMismatchData,
  topPrescribedMedicationsData,
  antibioticStewardshipData,
  polypharmacyRiskData,
  prescriptionPatternDeviationsData,
  formularyAdherenceData,
  topOrderedInvestigationsData,
  valueTestsData,
  repeatTestsData,
  turnaroundTimeData,
  investigationAccuracyData,
  providerDiagnosisAccuracyData,
  documentationCompletenessData,
  timeToTreatmentData,
  patientLoadVsOutcomeData,
  clinicalVariationData,
  patientJourneyData,
  chronicConditionData,
  treatmentPathwayAdherenceData,
  incompleteNotesData,
  timeToCloseCaseSheetData,
  referralPatternHeatmapData,
  diseasePrevalenceData,
  seasonalTrendData,
  highRiskPatientData,
  docCompletenessScoreData,
  prescriptionGuidelineAdherenceData
} from './data/sampleData';

// Function to flatten complex data structures for Excel
const flattenData = (data: any[], prefix = '') => {
  return data.map(item => {
    const flattened: any = {};
    Object.keys(item).forEach(key => {
      if (typeof item[key] === 'object' && item[key] !== null && !Array.isArray(item[key])) {
        // Flatten nested objects
        Object.keys(item[key]).forEach(nestedKey => {
          flattened[`${prefix}${key}_${nestedKey}`] = item[key][nestedKey];
        });
      } else if (Array.isArray(item[key])) {
        // Convert arrays to comma-separated strings
        flattened[`${prefix}${key}`] = item[key].join(', ');
      } else {
        flattened[`${prefix}${key}`] = item[key];
      }
    });
    return flattened;
  });
};

// Function to generate Excel file
export const generateMockDataExcel = () => {
  const workbook = XLSX.utils.book_new();

  // 1. Doctors Sheet
  const doctorsSheet = XLSX.utils.json_to_sheet(doctors);
  XLSX.utils.book_append_sheet(workbook, doctorsSheet, 'Doctors');

  // 2. Visits Sheet (first 100 visits to avoid Excel row limits)
  const visitsFlattened = flattenData(visits.slice(0, 100));
  const visitsSheet = XLSX.utils.json_to_sheet(visitsFlattened);
  XLSX.utils.book_append_sheet(workbook, visitsSheet, 'Visits_Sample');

  // 3. Top Diagnoses by Complaint
  const diagnosesFlattened = topDiagnosesByComplaintData.flatMap(item => 
    item.data.map(diagnosis => ({
      complaint: item.complaint,
      diagnosis_name: diagnosis.name,
      diagnosis_value: diagnosis.value
    }))
  );
  const diagnosesSheet = XLSX.utils.json_to_sheet(diagnosesFlattened);
  XLSX.utils.book_append_sheet(workbook, diagnosesSheet, 'Top_Diagnoses_by_Complaint');

  // 4. Symptom Progression
  const symptomSheet = XLSX.utils.json_to_sheet(symptomProgressionData);
  XLSX.utils.book_append_sheet(workbook, symptomSheet, 'Symptom_Progression');

  // 5. Treatment Efficacy
  const treatmentSheet = XLSX.utils.json_to_sheet(treatmentEfficacyData);
  XLSX.utils.book_append_sheet(workbook, treatmentSheet, 'Treatment_Efficacy');

  // 6. Diagnosis Mismatch
  const mismatchSheet = XLSX.utils.json_to_sheet(diagnosisMismatchData);
  XLSX.utils.book_append_sheet(workbook, mismatchSheet, 'Diagnosis_Mismatch');

  // 7. Top Prescribed Medications
  const medicationsSheet = XLSX.utils.json_to_sheet(topPrescribedMedicationsData);
  XLSX.utils.book_append_sheet(workbook, medicationsSheet, 'Top_Medications');

  // 8. Antibiotic Stewardship
  const antibioticSheet = XLSX.utils.json_to_sheet(antibioticStewardshipData);
  XLSX.utils.book_append_sheet(workbook, antibioticSheet, 'Antibiotic_Stewardship');

  // 9. Polypharmacy Risk
  const polypharmacySheet = XLSX.utils.json_to_sheet(polypharmacyRiskData);
  XLSX.utils.book_append_sheet(workbook, polypharmacySheet, 'Polypharmacy_Risk');

  // 10. Prescription Pattern Deviations
  const prescriptionSheet = XLSX.utils.json_to_sheet(prescriptionPatternDeviationsData);
  XLSX.utils.book_append_sheet(workbook, prescriptionSheet, 'Prescription_Patterns');

  // 11. Formulary Adherence
  const formularySheet = XLSX.utils.json_to_sheet(formularyAdherenceData);
  XLSX.utils.book_append_sheet(workbook, formularySheet, 'Formulary_Adherence');

  // 12. Top Ordered Investigations
  const topInvestigationsSheet = XLSX.utils.json_to_sheet(topOrderedInvestigationsData);
  XLSX.utils.book_append_sheet(workbook, topInvestigationsSheet, 'Top_Investigations');

  // 13. Value Tests
  const valueTestsSheet = XLSX.utils.json_to_sheet(valueTestsData);
  XLSX.utils.book_append_sheet(workbook, valueTestsSheet, 'Value_Tests');

  // 14. Repeat Tests
  const repeatTestsSheet = XLSX.utils.json_to_sheet(repeatTestsData);
  XLSX.utils.book_append_sheet(workbook, repeatTestsSheet, 'Repeat_Tests');

  // 15. Turnaround Time
  const turnaroundSheet = XLSX.utils.json_to_sheet(turnaroundTimeData);
  XLSX.utils.book_append_sheet(workbook, turnaroundSheet, 'Turnaround_Time');

  // 16. Investigation Accuracy
  const accuracySheet = XLSX.utils.json_to_sheet(investigationAccuracyData);
  XLSX.utils.book_append_sheet(workbook, accuracySheet, 'Investigation_Accuracy');

  // 17. Provider Diagnosis Accuracy
  const providerAccuracySheet = XLSX.utils.json_to_sheet(providerDiagnosisAccuracyData);
  XLSX.utils.book_append_sheet(workbook, providerAccuracySheet, 'Provider_Accuracy');

  // 18. Documentation Completeness
  const docCompletionSheet = XLSX.utils.json_to_sheet(documentationCompletenessData);
  XLSX.utils.book_append_sheet(workbook, docCompletionSheet, 'Doc_Completion');

  // 19. Time to Treatment
  const timeToTreatmentSheet = XLSX.utils.json_to_sheet(timeToTreatmentData);
  XLSX.utils.book_append_sheet(workbook, timeToTreatmentSheet, 'Time_to_Treatment');

  // 20. Patient Load vs Outcome
  const patientLoadSheet = XLSX.utils.json_to_sheet(patientLoadVsOutcomeData);
  XLSX.utils.book_append_sheet(workbook, patientLoadSheet, 'Patient_Load_vs_Outcome');

  // 21. Clinical Variation
  const clinicalVariationSheet = XLSX.utils.json_to_sheet(clinicalVariationData);
  XLSX.utils.book_append_sheet(workbook, clinicalVariationSheet, 'Clinical_Variation');

  // 22. Patient Journey (flattened)
  const journeyFlattened = patientJourneyData.map(event => ({
    date: event.date,
    type: event.type,
    details: event.details,
    iconPath: event.iconPath
  }));
  const journeySheet = XLSX.utils.json_to_sheet(journeyFlattened);
  XLSX.utils.book_append_sheet(workbook, journeySheet, 'Patient_Journey');

  // 23. Chronic Condition Data
  const chronicSheet = XLSX.utils.json_to_sheet(chronicConditionData);
  XLSX.utils.book_append_sheet(workbook, chronicSheet, 'Chronic_Conditions');

  // 24. Treatment Pathway Adherence
  const pathwaySheet = XLSX.utils.json_to_sheet(treatmentPathwayAdherenceData);
  XLSX.utils.book_append_sheet(workbook, pathwaySheet, 'Treatment_Pathway');

  // 25. Incomplete Notes
  const incompleteSheet = XLSX.utils.json_to_sheet(incompleteNotesData);
  XLSX.utils.book_append_sheet(workbook, incompleteSheet, 'Incomplete_Notes');

  // 26. Time to Close Case Sheet
  const timeToCloseSheet = XLSX.utils.json_to_sheet(timeToCloseCaseSheetData);
  XLSX.utils.book_append_sheet(workbook, timeToCloseSheet, 'Time_to_Close_Cases');

  // 27. Referral Pattern Heatmap (flattened)
  const referralFlattened = referralPatternHeatmapData.complaints.flatMap((complaint, i) =>
    referralPatternHeatmapData.specialties.map((specialty, j) => ({
      complaint,
      specialty,
      referral_count: referralPatternHeatmapData.data[i][j]
    }))
  );
  const referralSheet = XLSX.utils.json_to_sheet(referralFlattened);
  XLSX.utils.book_append_sheet(workbook, referralSheet, 'Referral_Patterns');

  // 28. Disease Prevalence
  const diseasePrevalenceSheet = XLSX.utils.json_to_sheet(diseasePrevalenceData);
  XLSX.utils.book_append_sheet(workbook, diseasePrevalenceSheet, 'Disease_Prevalence');

  // 29. Seasonal Trends
  const seasonalTrendSheet = XLSX.utils.json_to_sheet(seasonalTrendData);
  XLSX.utils.book_append_sheet(workbook, seasonalTrendSheet, 'Seasonal_Trends');

  // 30. High Risk Patients
  const highRiskSheet = XLSX.utils.json_to_sheet(highRiskPatientData);
  XLSX.utils.book_append_sheet(workbook, highRiskSheet, 'High_Risk_Patients');

  // 31. Documentation Completeness Score
  const docCompletenessSheet = XLSX.utils.json_to_sheet(docCompletenessScoreData);
  XLSX.utils.book_append_sheet(workbook, docCompletenessSheet, 'Doc_Completeness_Score');

  // 32. Prescription Guideline Adherence
  const guidelineSheet = XLSX.utils.json_to_sheet(prescriptionGuidelineAdherenceData);
  XLSX.utils.book_append_sheet(workbook, guidelineSheet, 'Guideline_Adherence');

  // 33. Complaint to Diagnosis Sankey (flattened)
  const sankeyFlattened = complaintToDiagnosisData.links.map(link => ({
    source: complaintToDiagnosisData.nodes[link.source].name,
    target: complaintToDiagnosisData.nodes[link.target].name,
    value: link.value
  }));
  const sankeySheet = XLSX.utils.json_to_sheet(sankeyFlattened);
  XLSX.utils.book_append_sheet(workbook, sankeySheet, 'Complaint_to_Diagnosis');

  // Summary Sheet
  const summaryData = [
    { Sheet_Name: 'Doctors', Description: 'List of doctors in the system', Record_Count: doctors.length },
    { Sheet_Name: 'Visits_Sample', Description: 'Sample of patient visits (first 100)', Record_Count: 100 },
    { Sheet_Name: 'Top_Diagnoses_by_Complaint', Description: 'Top diagnoses for each complaint type', Record_Count: diagnosesFlattened.length },
    { Sheet_Name: 'Symptom_Progression', Description: 'Patient symptom progression over visits', Record_Count: symptomProgressionData.length },
    { Sheet_Name: 'Treatment_Efficacy', Description: 'Treatment efficacy comparison data', Record_Count: treatmentEfficacyData.length },
    { Sheet_Name: 'Diagnosis_Mismatch', Description: 'Diagnosis mismatch rates by specialty', Record_Count: diagnosisMismatchData.length },
    { Sheet_Name: 'Top_Medications', Description: 'Most prescribed medications', Record_Count: topPrescribedMedicationsData.length },
    { Sheet_Name: 'Antibiotic_Stewardship', Description: 'Antibiotic usage patterns', Record_Count: antibioticStewardshipData.length },
    { Sheet_Name: 'Polypharmacy_Risk', Description: 'Polypharmacy risk distribution', Record_Count: polypharmacyRiskData.length },
    { Sheet_Name: 'Prescription_Patterns', Description: 'Doctor prescription pattern deviations', Record_Count: prescriptionPatternDeviationsData.length },
    { Sheet_Name: 'Formulary_Adherence', Description: 'Formulary adherence rates', Record_Count: formularyAdherenceData.length },
    { Sheet_Name: 'Top_Investigations', Description: 'Most ordered investigations', Record_Count: topOrderedInvestigationsData.length },
    { Sheet_Name: 'Value_Tests', Description: 'High vs low value test distribution', Record_Count: valueTestsData.length },
    { Sheet_Name: 'Repeat_Tests', Description: 'Repeat test frequencies', Record_Count: repeatTestsData.length },
    { Sheet_Name: 'Turnaround_Time', Description: 'Investigation turnaround times', Record_Count: turnaroundTimeData.length },
    { Sheet_Name: 'Investigation_Accuracy', Description: 'Investigation accuracy rates', Record_Count: investigationAccuracyData.length },
    { Sheet_Name: 'Provider_Accuracy', Description: 'Provider diagnosis accuracy', Record_Count: providerDiagnosisAccuracyData.length },
    { Sheet_Name: 'Doc_Completion', Description: 'Documentation completion rates', Record_Count: documentationCompletenessData.length },
    { Sheet_Name: 'Time_to_Treatment', Description: 'Time to treatment by provider', Record_Count: timeToTreatmentData.length },
    { Sheet_Name: 'Patient_Load_vs_Outcome', Description: 'Patient load vs outcome correlation', Record_Count: patientLoadVsOutcomeData.length },
    { Sheet_Name: 'Clinical_Variation', Description: 'Clinical practice variation', Record_Count: clinicalVariationData.length },
    { Sheet_Name: 'Patient_Journey', Description: 'Sample patient journey events', Record_Count: patientJourneyData.length },
    { Sheet_Name: 'Chronic_Conditions', Description: 'Chronic condition monitoring data', Record_Count: chronicConditionData.length },
    { Sheet_Name: 'Treatment_Pathway', Description: 'Treatment pathway adherence', Record_Count: treatmentPathwayAdherenceData.length },
    { Sheet_Name: 'Incomplete_Notes', Description: 'Incomplete documentation categories', Record_Count: incompleteNotesData.length },
    { Sheet_Name: 'Time_to_Close_Cases', Description: 'Case closure times by specialty', Record_Count: timeToCloseCaseSheetData.length },
    { Sheet_Name: 'Referral_Patterns', Description: 'Referral patterns heatmap data', Record_Count: referralFlattened.length },
    { Sheet_Name: 'Disease_Prevalence', Description: 'Disease prevalence in population', Record_Count: diseasePrevalenceData.length },
    { Sheet_Name: 'Seasonal_Trends', Description: 'Seasonal disease trends', Record_Count: seasonalTrendData.length },
    { Sheet_Name: 'High_Risk_Patients', Description: 'High risk patient categories', Record_Count: highRiskPatientData.length },
    { Sheet_Name: 'Doc_Completeness_Score', Description: 'Documentation completeness scores', Record_Count: docCompletenessScoreData.length },
    { Sheet_Name: 'Guideline_Adherence', Description: 'Prescription guideline adherence', Record_Count: prescriptionGuidelineAdherenceData.length },
    { Sheet_Name: 'Complaint_to_Diagnosis', Description: 'Complaint to diagnosis flow data', Record_Count: sankeyFlattened.length }
  ];

  const summarySheet = XLSX.utils.json_to_sheet(summaryData);
  XLSX.utils.book_append_sheet(workbook, summarySheet, 'Data_Summary');

  // Write the file
  XLSX.writeFile(workbook, 'AI_Clinical_Dashboard_Mock_Data.xlsx');
  console.log('Excel file generated successfully: AI_Clinical_Dashboard_Mock_Data.xlsx');
  console.log(`Total sheets created: ${workbook.SheetNames.length}`);
  console.log('Sheets:', workbook.SheetNames.join(', '));
};

// Run the function if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  generateMockDataExcel();
}
