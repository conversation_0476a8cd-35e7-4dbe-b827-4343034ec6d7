
import React, { useState } from 'react';
import Header from './components/Header';
import Dashboard from './components/Dashboard';
import Sidebar from './components/Sidebar';
import { DASHBOARDS } from './constants';

// Statically import all dashboard components
import ClinicalDecisionSupport from './dashboards/ClinicalDecisionSupport';
import PrescribingMedication from './dashboards/PrescribingMedication';
import InvestigationsUtilization from './dashboards/InvestigationsUtilization';
import ProviderPerformance from './dashboards/ProviderPerformance';
import PatientJourney from './dashboards/PatientJourney';
import OperationalQuality from './dashboards/OperationalQuality';
import PopulationHealth from './dashboards/PopulationHealth';
import ComplianceQuality from './dashboards/ComplianceQuality';
import DashboardWireframes from './dashboards/DashboardWireframes';

const dashboardComponents: { [key: string]: React.FC<{}> } = {
  clinicalDecisionSupport: ClinicalDecisionSupport,
  prescribingMedication: PrescribingMedication,
  investigationsUtilization: InvestigationsUtilization,
  providerPerformance: ProviderPerformance,
  patientJourney: PatientJourney,
  operationalQuality: OperationalQuality,
  populationHealth: PopulationHealth,
  complianceQualityOfCare: ComplianceQuality,
  dashboardWireframes: DashboardWireframes,
};

function App() {
  const [viewMode, setViewMode] = useState<'original' | 'clinical'>('original');
  const [activeDashboard, setActiveDashboard] = useState<string>(DASHBOARDS[0].id);

  const ActiveDashboardComponent = dashboardComponents[activeDashboard];
  const activeDashboardInfo = DASHBOARDS.find(d => d.id === activeDashboard);

  if (viewMode === 'clinical') {
    return (
      <div className="flex h-screen bg-light-bg font-sans">
        <Sidebar activeDashboard={activeDashboard} setActiveDashboard={setActiveDashboard} />
        <main className="flex-1 p-6 lg:p-8 overflow-y-auto">
          <header className="mb-6 flex justify-between items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-800">{activeDashboardInfo?.name}</h2>
              <p className="text-gray-500 mt-1">Key metrics and visualizations for {activeDashboardInfo?.name.toLowerCase()}.</p>
            </div>
            <button
              onClick={() => setViewMode('original')}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Switch to Original Dashboard
            </button>
          </header>
          {ActiveDashboardComponent && <ActiveDashboardComponent />}
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-neutral-light font-sans text-neutral-dark">
      <Header />
      <main className="p-4 sm:p-6 lg:p-8">
        <div className="mb-6 flex justify-end">
          <button
            onClick={() => setViewMode('clinical')}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
          >
            Switch to Clinical Intelligence
          </button>
        </div>
        <Dashboard />
      </main>
    </div>
  );
}

export default App;
