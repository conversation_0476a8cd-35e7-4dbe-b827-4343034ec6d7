
import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, Responsive<PERSON>ontainer, <PERSON><PERSON><PERSON>, Pie, Cell } from 'recharts';
import Card from '../components/Card';
import { topPrescribedMedicationsData, antibioticStewardshipData, polypharmacyRiskData, prescriptionPatternDeviationsData, formularyAdherenceData } from '../data/sampleData';
import { CHART_COLORS } from '../constants';

const PrescribingMedication: React.FC = () => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 2xl:grid-cols-3 gap-6">
      <Card
        title="Top Prescribed Medications"
        description="Overall drug usage patterns across the institution."
      >
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={topPrescribedMedicationsData} margin={{ top: 5, right: 30, left: 20, bottom: 60 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" angle={-45} textAnchor="end" interval={0} />
            <YAxis />
            <Tooltip />
            <Bar dataKey="value" name="Prescriptions" fill={CHART_COLORS[0]} />
          </BarChart>
        </ResponsiveContainer>
      </Card>

      <Card
        title="Antibiotic Stewardship"
        description="Monitoring antibiotic usage for viral vs. bacterial complaints."
      >
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={antibioticStewardshipData} layout="vertical" barSize={20} margin={{ top: 5, right: 30, left: 50, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis type="number" />
            <YAxis type="category" dataKey="name" width={100} />
            <Tooltip />
            <Legend />
            <Bar dataKey="Antibiotics" stackId="a" fill={CHART_COLORS[3]} />
            <Bar dataKey="No Antibiotics" stackId="a" fill={CHART_COLORS[1]} />
          </BarChart>
        </ResponsiveContainer>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card
          title="Polypharmacy Risk"
          description="% of patients on 5+ medications."
        >
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie data={polypharmacyRiskData} dataKey="value" nameKey="name" cx="50%" cy="50%" outerRadius={80} label>
                <Cell fill={CHART_COLORS[3]} />
                <Cell fill={CHART_COLORS[4]} />
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </Card>
        <Card
          title="Formulary Adherence"
          description="% of prescriptions aligned with approved formulary."
        >
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie data={formularyAdherenceData} dataKey="value" nameKey="name" cx="50%" cy="50%" innerRadius={60} outerRadius={80} paddingAngle={5} label>
                <Cell fill={CHART_COLORS[1]} />
                <Cell fill={CHART_COLORS[2]} />
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </Card>
      </div>
      
      <Card
        title="Prescription Pattern Deviations"
        description="Identifying outlier prescribing behaviors by physician."
        className="lg:col-span-2 2xl:col-span-3"
      >
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={prescriptionPatternDeviationsData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Bar dataKey="Opioids" fill={CHART_COLORS[0]} />
            <Bar dataKey="Antibiotics" fill={CHART_COLORS[1]} />
            <Bar dataKey="Statins" fill={CHART_COLORS[2]} />
          </BarChart>
        </ResponsiveContainer>
      </Card>
    </div>
  );
};

export default PrescribingMedication;