
import React from 'react';
import Card from '../components/Card';
import { incompleteNotesData, timeToCloseCaseSheetData, referralPatternHeatmapData } from '../data/sampleData';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, Cell, PieChart, Pie } from 'recharts';
import { CHART_COLORS } from '../constants';

const HeatmapCell = ({ value }: { value: number }) => {
  const intensity = Math.min(Math.floor(value / 10), 9);
  const colorClasses = [
    'bg-sky-50', 'bg-sky-100', 'bg-sky-200', 'bg-sky-300', 'bg-sky-400', 
    'bg-sky-500', 'bg-sky-600', 'bg-sky-700', 'bg-sky-800', 'bg-sky-900'
  ];
  const textColor = intensity > 4 ? 'text-white' : 'text-gray-800';
  return (
    <div className={`w-full h-full flex items-center justify-center ${colorClasses[intensity]} ${textColor}`}>
      {value}%
    </div>
  );
};

const OperationalQuality: React.FC = () => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card
        title="Incomplete Notes"
        description="Visits with missing diagnosis, treatment, or education data."
      >
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={incompleteNotesData}
              cx="50%"
              cy="50%"
              labelLine={false}
              outerRadius={100}
              fill="#8884d8"
              dataKey="value"
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
            >
              {incompleteNotesData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
              ))}
            </Pie>
            <Tooltip formatter={(value) => `${value} notes`} />
          </PieChart>
        </ResponsiveContainer>
      </Card>
      
      <Card
        title="Average Time to Close Case Sheet"
        description="Duration (hours) between admission and completed documentation by specialty."
      >
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={timeToCloseCaseSheetData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Bar dataKey="value" name="Hours" fill={CHART_COLORS[1]}>
              {timeToCloseCaseSheetData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.name.includes('Average') ? '#a0aec0' : CHART_COLORS[1]} />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </Card>

      <div className="lg:col-span-2 bg-white rounded-lg shadow-md p-6 hover:shadow-xl transition-shadow duration-300">
        <h3 className="text-xl font-bold text-gray-800 mb-2">Referral Pattern Heatmap</h3>
        <p className="text-gray-600 mb-4 text-sm">Which complaints most often lead to referral to other specialties (% of cases).</p>
        <div className="flex">
            <div className="flex flex-col justify-around text-sm font-semibold text-right pr-4">
              {referralPatternHeatmapData.complaints.map(c => <div key={c} className="h-12 flex items-center">{c}</div>)}
            </div>
            <div className="flex-grow">
                <div className="grid grid-cols-4 text-center text-sm font-semibold mb-2">
                    {referralPatternHeatmapData.specialties.map(s => <div key={s}>{s}</div>)}
                </div>
                <div className="grid grid-cols-4 gap-1">
                    {referralPatternHeatmapData.data.flat().map((value, index) => (
                        <div key={index} className="h-12 rounded">
                            <HeatmapCell value={value} />
                        </div>
                    ))}
                </div>
            </div>
        </div>
      </div>
    </div>
  );
};

export default OperationalQuality;