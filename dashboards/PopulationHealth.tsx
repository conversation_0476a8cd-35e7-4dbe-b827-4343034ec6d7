
import React from 'react';
import Card from '../components/Card';
import { diseasePrevalenceData, seasonalTrendData, highRiskPatientData } from '../data/sampleData';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, Cell, LineChart, Line, ComposedChart } from 'recharts';
import { CHART_COLORS } from '../constants';

const PopulationHealth: React.FC = () => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card
        title="Disease Prevalence by Region/Unit"
        description="Top chronic diseases in the patient population."
        className="lg:col-span-2"
      >
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={diseasePrevalenceData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Bar dataKey="value" name="Patient Count" fill={CHART_COLORS[0]}>
              {diseasePrevalenceData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </Card>

      <Card
        title="Seasonal Trend Analysis"
        description="Surge in complaints by season (e.g., respiratory in winter)."
        className="lg:col-span-2"
      >
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={seasonalTrendData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Line type="monotone" dataKey="Respiratory" stroke={CHART_COLORS[1]} name="Respiratory Cases" />
            <Line type="monotone" dataKey="GI" stroke={CHART_COLORS[2]} name="GI Cases" />
          </LineChart>
        </ResponsiveContainer>
      </Card>

      <Card
        title="High-Risk Patient Flags"
        description="Patients with frequent visits, polypharmacy, and multiple diagnoses."
        className="lg:col-span-2"
      >
        <ResponsiveContainer width="100%" height="100%">
          <ComposedChart layout="vertical" data={highRiskPatientData} margin={{ top: 20, right: 20, bottom: 20, left: 100 }}>
            <CartesianGrid stroke="#f5f5f5" />
            <XAxis type="number" />
            <YAxis dataKey="name" type="category" scale="band" width={100} />
            <Tooltip />
            <Bar dataKey="value" name="Patient Count" barSize={30}>
               {highRiskPatientData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={CHART_COLORS[(index + 3) % CHART_COLORS.length]} />
              ))}
            </Bar>
          </ComposedChart>
        </ResponsiveContainer>
      </Card>
    </div>
  );
};

export default PopulationHealth;