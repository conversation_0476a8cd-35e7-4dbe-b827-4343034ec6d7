
import React from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON><PERSON><PERSON>s, CartesianGrid, <PERSON>ltip, Legend, ResponsiveContainer, PieChart, Pie, Cell, ComposedChart, Area } from 'recharts';
import Card from '../components/Card';
import { topOrderedInvestigationsData, valueTestsData, repeatTestsData, turnaroundTimeData, investigationAccuracyData } from '../data/sampleData';
import { CHART_COLORS } from '../constants';

const InvestigationsUtilization: React.FC = () => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 2xl:grid-cols-3 gap-6">
      <Card
        title="Top Ordered Investigations"
        description="Most frequently ordered tests across departments."
      >
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={topOrderedInvestigationsData} layout="vertical" margin={{ top: 5, right: 30, left: 120, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis type="number" />
            <YAxis type="category" dataKey="name" width={120} tick={{fontSize: 12}} />
            <Tooltip />
            <Bar dataKey="value" name="Orders" fill={CHART_COLORS[0]} />
          </BarChart>
        </ResponsiveContainer>
      </Card>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card
          title="High-Value vs Low-Value Tests"
          description="Based on clinical outcome and cost-effectiveness."
        >
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie data={valueTestsData} dataKey="value" nameKey="name" cx="50%" cy="50%" outerRadius={80} label>
                <Cell fill={CHART_COLORS[1]} />
                <Cell fill={CHART_COLORS[3]} />
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </Card>
        <Card
          title="Repeat Tests Alert"
          description="Top tests repeated on same patient in short span."
        >
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie data={repeatTestsData} dataKey="value" nameKey="name" cx="50%" cy="50%" innerRadius={60} outerRadius={80} labelLine={false} label>
                {repeatTestsData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
              <Legend layout="vertical" align="right" verticalAlign="middle" />
            </PieChart>
          </ResponsiveContainer>
        </Card>
      </div>

      <Card
        title="Investigation to Diagnosis Accuracy"
        description="Which tests are most decisive in confirming diagnoses."
      >
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={investigationAccuracyData} margin={{ top: 5, right: 30, left: 20, bottom: 50 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" angle={-30} textAnchor="end" interval={0} />
            <YAxis domain={[60, 100]} label={{ value: 'Accuracy %', angle: -90, position: 'insideLeft' }}/>
            <Tooltip />
            <Bar dataKey="value" name="Accuracy (%)" fill={CHART_COLORS[2]}>
              {investigationAccuracyData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </Card>

      <Card
        title="Turnaround Time (TAT)"
        description="From order to result availability — lab & imaging (in minutes)."
        className="lg:col-span-2 2xl:col-span-3"
      >
        <ResponsiveContainer width="100%" height="100%">
          <ComposedChart data={turnaroundTimeData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Area type="monotone" dataKey="Target (min)" fill="#8884d8" stroke="#8884d8" opacity={0.3} />
            <Bar dataKey="Actual (min)" barSize={40} fill={CHART_COLORS[0]} />
          </ComposedChart>
        </ResponsiveContainer>
      </Card>
    </div>
  );
};

export default InvestigationsUtilization;