import React from 'react';

const WireframeCard: React.FC<{ title: string; children: React.ReactNode }> = ({ title, children }) => (
  <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-xl transition-shadow duration-300 h-full">
    <h3 className="text-xl font-bold text-gray-800 mb-4 border-b pb-2">{title}</h3>
    <div className="space-y-4">
      {children}
    </div>
  </div>
);

const WireframeSection: React.FC<{ title: string; items: string[] }> = ({ title, items }) => (
  <div>
    <h4 className="text-md font-semibold text-brand-primary mb-2">{title}</h4>
    <ul className="list-disc list-inside text-gray-600 space-y-1 text-sm">
      {items.map((item, index) => <li key={index}>{item}</li>)}
    </ul>
  </div>
);

const DashboardWireframes: React.FC = () => {
  const wireframes = [
    {
      title: "1. Clinical Overview Dashboard",
      sections: [
        { title: "Filters", items: ["Department | Date Range | Doctor | Complaint"] },
        { title: "KPIs", items: ["Total Visits", "% with Treatment Plan", "% with Prescriptions", "Most Common Complaints", "Most Common Diagnoses"] },
        { title: "Charts", items: ["📈 Bar Chart: Top 10 Diagnoses", "🧭 Pie Chart: Complaint Distribution", "📊 Stacked Bar: Complaint → Diagnosis Mapping", "📆 Time Series: Daily Patient Load"] }
      ]
    },
    {
      title: "2. Prescription Trends Dashboard",
      sections: [
        { title: "Filters", items: ["Department | Doctor | Diagnosis"] },
        { title: "KPIs", items: ["Top Prescribed Medications", "Avg. Meds per Patient", "Antibiotic Usage %"] },
        { title: "Charts", items: ["📈 Line Chart: Prescriptions over Time", "🧾 Treemap: Drug Class Distribution", "📊 Bar Chart: Doctor-wise Prescription Load"] }
      ]
    },
    {
      title: "3. Investigation Insights Dashboard",
      sections: [
        { title: "Filters", items: ["Department | Investigation Type"] },
        { title: "KPIs", items: ["Top Ordered Tests", "Avg. Tests per Visit", "% Confirmatory (i.e. linked to final diagnosis)"] },
        { title: "Charts", items: ["📉 Bar Chart: Most Ordered Investigations", "⏱️ Line Chart: TAT from Order → Result", "📊 Heatmap: Test Volume by Day/Hour"] }
      ]
    },
    {
      title: "4. Patient Journey Dashboard",
      sections: [
        { title: "Filters", items: ["Patient | Date Range"] },
        { title: "Timeline View", items: ["Complaint → Assessment → Diagnosis → Tests → Medications"] },
        { title: "Additional Widgets", items: ["Past Visit Notes", "Progress of Chronic Conditions", "Risk Score (if ML is applied)"] }
      ]
    },
    {
      title: "5. Documentation Quality Dashboard",
      sections: [
        { title: "Filters", items: ["Doctor | Department | Date"] },
        { title: "KPIs", items: ["Avg. Documentation Score (based on filled fields)", "% of Notes with Complete Documentation", "Missing Section Flags (Diagnosis / Education / Plan)"] },
        { title: "Charts", items: ["📊 Radar Chart: Doctor-wise Completeness", "🧾 Table: Top Missing Documentation Cases"] }
      ]
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
      {wireframes.map(wf => (
        <WireframeCard key={wf.title} title={wf.title}>
          {wf.sections.map(sec => (
            <WireframeSection key={sec.title} title={sec.title} items={sec.items} />
          ))}
        </WireframeCard>
      ))}
    </div>
  );
};

export default DashboardWireframes;