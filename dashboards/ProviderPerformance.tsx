
import React from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON>Axis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, Cell, ScatterChart, Scatter, Line, ComposedChart } from 'recharts';
import Card from '../components/Card';
import { providerDiagnosisAccuracyData, documentationCompletenessData, timeToTreatmentData, patientLoadVsOutcomeData, clinicalVariationData } from '../data/sampleData';
import { CHART_COLORS } from '../constants';

const ProviderPerformance: React.FC = () => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 2xl:grid-cols-3 gap-6">
      <Card
        title="Diagnosis Accuracy"
        description="Match between initial assessment and final diagnosis."
      >
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={providerDiagnosisAccuracyData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis domain={[80, 100]} label={{ value: 'Accuracy %', angle: -90, position: 'insideLeft' }}/>
            <Tooltip />
            <Bar dataKey="value" name="Accuracy" fill={CHART_COLORS[1]}>
              {providerDiagnosisAccuracyData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.name === 'Dept. Average' ? '#a0aec0' : CHART_COLORS[1]} />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </Card>

      <Card
        title="Documentation Completeness"
        description="Which doctors consistently complete all note sections."
      >
        <ResponsiveContainer width="100%" height="100%">
          <ComposedChart layout="vertical" data={documentationCompletenessData} margin={{ top: 20, right: 20, bottom: 20, left: 50 }}>
            <CartesianGrid stroke="#f5f5f5" />
            <XAxis type="number" domain={[0, 100]} />
            <YAxis dataKey="name" type="category" scale="band" />
            <Tooltip />
            <Legend />
            <Bar dataKey="value" barSize={20} fill={CHART_COLORS[2]}>
               {documentationCompletenessData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
              ))}
            </Bar>
          </ComposedChart>
        </ResponsiveContainer>
      </Card>

      <Card
        title="Time-to-Treatment Plan (minutes)"
        description="How quickly treatment plans are documented after diagnosis."
      >
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={timeToTreatmentData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis label={{ value: 'Minutes', angle: -90, position: 'insideLeft' }}/>
            <Tooltip />
            <Bar dataKey="value" name="Time (min)" fill={CHART_COLORS[3]}>
              {timeToTreatmentData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.name.includes('Average') ? '#a0aec0' : CHART_COLORS[3]} />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </Card>

      <Card
        title="Patient Load & Outcome"
        description="Correlate doctor’s daily cases with patient satisfaction/outcome score."
      >
        <ResponsiveContainer width="100%" height="100%">
          <ScatterChart margin={{ top: 20, right: 20, bottom: 20, left: 20, }}>
            <CartesianGrid />
            <XAxis type="number" dataKey="cases" name="Daily Cases" unit=" cases" />
            <YAxis type="number" dataKey="outcome" name="Outcome Score" unit="/5" domain={[3, 5]}/>
            <Tooltip cursor={{ strokeDasharray: '3 3' }} />
            <Scatter name="Provider" data={patientLoadVsOutcomeData} fill={CHART_COLORS[4]} />
          </ScatterChart>
        </ResponsiveContainer>
      </Card>

      <Card
        title="Clinical Variation"
        description="How different doctors handle similar complaints (e.g., uncomplicated low back pain)."
        className="lg:col-span-2"
      >
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={clinicalVariationData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Bar dataKey="Test A" stackId="tests" fill={CHART_COLORS[0]} />
            <Bar dataKey="Test B" stackId="tests" fill={CHART_COLORS[1]} />
            <Bar dataKey="Med X" stackId="meds" fill={CHART_COLORS[2]} />
            <Bar dataKey="Med Y" stackId="meds" fill={CHART_COLORS[3]} />
          </BarChart>
        </ResponsiveContainer>
      </Card>

    </div>
  );
};

export default ProviderPerformance;