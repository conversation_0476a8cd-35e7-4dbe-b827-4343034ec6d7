
import React from 'react';
import Card from '../components/Card';
import { patientJourneyData, chronicConditionData, treatmentPathwayAdherenceData } from '../data/sampleData';
import { ResponsiveContainer, LineChart, Line, CartesianGrid, XAxis, YAxis, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell } from 'recharts';
import { CHART_COLORS } from '../constants';

const EventIcon = ({ path }: { path: string }) => (
    <span className="absolute flex items-center justify-center w-8 h-8 bg-brand-primary rounded-full -left-4 ring-8 ring-white dark:ring-gray-800">
        <svg className="w-4 h-4 text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
             <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={path} />
        </svg>
    </span>
);

const PatientJourney: React.FC = () => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <div className="lg:col-span-2 bg-white rounded-lg shadow-md p-6 hover:shadow-xl transition-shadow duration-300">
        <h3 className="text-xl font-bold text-gray-800 mb-2">Visit Timeline per Patient</h3>
        <p className="text-gray-600 mb-6 text-sm">Visual timeline of complaints → investigations → diagnosis → treatment for a sample patient.</p>
        <div className="h-[28rem] overflow-y-auto pr-4">
          <ol className="relative border-l border-gray-200 dark:border-gray-700">
            {patientJourneyData.map((event, index) => (
              <li key={index} className="mb-10 ml-10">
                <EventIcon path={event.iconPath} />
                <h3 className="flex items-center mb-1 text-lg font-semibold text-gray-900">{event.type}</h3>
                <time className="block mb-2 text-sm font-normal leading-none text-gray-400 dark:text-gray-500">{event.date}</time>
                <p className="mb-4 text-base font-normal text-gray-500 dark:text-gray-400">{event.details}</p>
              </li>
            ))}
          </ol>
        </div>
      </div>
      
      <div className="flex flex-col gap-6">
        <Card
          title="Chronic Condition Tracking"
          description="Assessment & treatment response over time for Diabetes & HTN."
        >
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={chronicConditionData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis yAxisId="left" stroke={CHART_COLORS[0]} />
              <YAxis yAxisId="right" orientation="right" stroke={CHART_COLORS[1]} />
              <Tooltip />
              <Legend />
              <Line yAxisId="left" type="monotone" dataKey="HbA1c" stroke={CHART_COLORS[0]} />
              <Line yAxisId="right" type="monotone" dataKey="BP (Systolic)" stroke={CHART_COLORS[1]} />
            </LineChart>
          </ResponsiveContainer>
        </Card>

        <Card
          title="Treatment Pathway Compliance"
          description="Adherence to evidence-based clinical pathways."
        >
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie data={treatmentPathwayAdherenceData} dataKey="value" nameKey="name" cx="50%" cy="50%" innerRadius={60} outerRadius={80} paddingAngle={5} label>
                <Cell fill={CHART_COLORS[1]} />
                <Cell fill={CHART_COLORS[3]} />
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </Card>
      </div>
    </div>
  );
};

export default PatientJourney;