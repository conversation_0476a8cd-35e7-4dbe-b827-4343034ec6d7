
import React, { useState } from 'react';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, LineChart, Line, Sankey, Cell } from 'recharts';
import Card from '../components/Card';
import { topDiagnosesByComplaintData, complaintToDiagnosisData, symptomProgressionData, treatmentEfficacyData, diagnosisMismatchData } from '../data/sampleData';
import { CHART_COLORS } from '../constants';

const ClinicalDecisionSupport: React.FC = () => {
  const [selectedComplaint, setSelectedComplaint] = useState(topDiagnosesByComplaintData[0].complaint);
  const activeComplaintData = topDiagnosesByComplaintData.find(d => d.complaint === selectedComplaint)?.data || [];

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 2xl:grid-cols-3 gap-6">
      <Card
        title="Top Diagnoses by Complaint"
        description="Most common diagnoses for a selected chief complaint."
        className="lg:col-span-2"
      >
        <div className="mb-4">
          {topDiagnosesByComplaintData.map(item => (
            <button
              key={item.complaint}
              onClick={() => setSelectedComplaint(item.complaint)}
              className={`px-3 py-1 text-sm rounded-full mr-2 mb-2 transition-colors ${selectedComplaint === item.complaint ? 'bg-brand-primary text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
            >
              {item.complaint}
            </button>
          ))}
        </div>
        <ResponsiveContainer width="100%" height="80%">
          <BarChart data={activeComplaintData} layout="vertical" margin={{ top: 5, right: 30, left: 50, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis type="number" />
            <YAxis type="category" dataKey="name" width={100} tick={{ fontSize: 12 }} />
            <Tooltip />
            <Legend />
            <Bar dataKey="value" name="Cases" fill={CHART_COLORS[0]} />
          </BarChart>
        </ResponsiveContainer>
      </Card>
      
      <Card
        title="Complaint-to-Diagnosis Mapping"
        description="Visualization of how initial complaints resolve into final diagnoses."
      >
        <ResponsiveContainer width="100%" height="100%">
          <Sankey
            data={complaintToDiagnosisData}
            node={{ stroke: '#777', strokeWidth: 2 }}
            nodePadding={50}
            margin={{ top: 20, right: 20, bottom: 20, left: 20 }}
            link={{ stroke: '#777', strokeOpacity: 0.5 }}
          >
            <Tooltip />
             {complaintToDiagnosisData.nodes.map((node, i) => (
                <Cell key={`cell-${i}`} fill={CHART_COLORS[i % CHART_COLORS.length]} />
              ))}
          </Sankey>
        </ResponsiveContainer>
      </Card>

      <Card
        title="Symptom Progression Trends"
        description="How patient-reported symptoms evolve over multiple visits."
      >
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={symptomProgressionData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Line type="monotone" dataKey="Pain Score" stroke={CHART_COLORS[0]} activeDot={{ r: 8 }} />
            <Line type="monotone" dataKey="Nausea Score" stroke={CHART_COLORS[1]} />
          </LineChart>
        </ResponsiveContainer>
      </Card>

       <Card
        title="Treatment Efficacy Tracker"
        description="Average symptom score improvement vs. applied treatments."
      >
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={treatmentEfficacyData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis label={{ value: 'Avg. Symptom Score', angle: -90, position: 'insideLeft' }} />
            <Tooltip />
            <Legend />
            <Line type="monotone" dataKey="Treatment A" stroke={CHART_COLORS[0]} />
            <Line type="monotone" dataKey="Treatment B" stroke={CHART_COLORS[1]} />
            <Line type="monotone" dataKey="Placebo" stroke={CHART_COLORS[3]} strokeDasharray="5 5"/>
          </LineChart>
        </ResponsiveContainer>
      </Card>

      <Card
        title="Assessment vs. Diagnosis Mismatch"
        description="Cases where working diagnosis changed from initial assessment, by specialty."
      >
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={diagnosisMismatchData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Bar dataKey="value" name="Mismatched Cases" fill={CHART_COLORS[2]}>
               {diagnosisMismatchData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </Card>
    </div>
  );
};

export default ClinicalDecisionSupport;