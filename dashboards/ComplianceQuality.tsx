
import React from 'react';
import Card from '../components/Card';
import { docCompletenessScoreData, prescriptionGuidelineAdherenceData } from '../data/sampleData';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, Cell, PieChart, Pie } from 'recharts';
import { CHART_COLORS } from '../constants';

const ComplianceQuality: React.FC = () => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card
        title="Clinical Documentation Completeness Score"
        description="Overall % of case sheets with all mandatory sections filled."
      >
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={docCompletenessScoreData}
              dataKey="value"
              nameKey="name"
              cx="50%"
              cy="50%"
              innerRadius={70}
              outerRadius={100}
              paddingAngle={5}
              label={({ name, value }) => `${name}: ${value}%`}
            >
              <Cell fill={CHART_COLORS[1]} />
              <Cell fill={CHART_COLORS[3]} />
            </Pie>
            <Tooltip />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      </Card>

      <Card
        title="Investigation Justification Score"
        description="A proxy score correlating test orders with actual diagnostic yield."
      >
        <div className="flex flex-col items-center justify-center h-full">
            <div className="relative w-48 h-48">
                <svg className="w-full h-full" viewBox="0 0 36 36">
                    <path className="text-gray-200"
                        strokeWidth="3.8"
                        stroke="currentColor"
                        fill="none"
                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    />
                    <path className="text-accent-green"
                        strokeWidth="3.8"
                        strokeDasharray="91, 100"
                        strokeLinecap="round"
                        stroke="currentColor"
                        fill="none"
                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    />
                </svg>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center">
                    <span className="text-3xl font-bold text-gray-700">91%</span>
                    <span className="block text-sm text-gray-500">High Yield</span>
                </div>
            </div>
        </div>
      </Card>
      
      <Card
        title="Prescription Guidelines Adherence"
        description="Are drugs aligned with diagnosis guidelines for common conditions?"
        className="lg:col-span-2"
      >
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={prescriptionGuidelineAdherenceData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis label={{ value: '% Adherence', angle: -90, position: 'insideLeft' }}/>
            <Tooltip />
            <Legend />
            <Bar dataKey="Adherent" stackId="a" fill={CHART_COLORS[1]} />
            <Bar dataKey="Non-Adherent" stackId="a" fill={CHART_COLORS[3]} />
          </BarChart>
        </ResponsiveContainer>
      </Card>
    </div>
  );
};

export default ComplianceQuality;