
import type { NameValueData, MultiBarData, LineData, SankeyData, PatientJourneyEvent } from '../types';

// 1. Clinical Decision Support
export const topDiagnosesByComplaintData: { complaint: string; data: NameValueData[] }[] = [
    {
        complaint: 'Chest Pain',
        data: [
            { name: '<PERSON><PERSON>', value: 45 },
            { name: 'GERD', value: 30 },
            { name: 'Costochondritis', value: 15 },
            { name: 'Panic Attack', value: 10 },
        ],
    },
    {
        complaint: 'Headache',
        data: [
            { name: 'Migraine', value: 60 },
            { name: 'Tension Headache', value: 25 },
            { name: 'C<PERSON> Headache', value: 10 },
            { name: 'Sinus<PERSON>', value: 5 },
        ],
    },
];

export const complaintToDiagnosisData: SankeyData = {
    nodes: [
        { name: 'Chest Pain' },
        { name: 'Headache' },
        { name: 'Abdominal Pain' },
        { name: '<PERSON><PERSON>' },
        { name: '<PERSON><PERSON>' },
        { name: '<PERSON><PERSON><PERSON>' },
        { name: '<PERSON><PERSON><PERSON>' },
        { name: 'Appendici<PERSON>' },
    ],
    links: [
        { source: 0, target: 3, value: 45 },
        { source: 0, target: 4, value: 30 },
        { source: 1, target: 5, value: 60 },
        { source: 2, target: 6, value: 50 },
        { source: 2, target: 7, value: 20 },
        { source: 2, target: 4, value: 10 },
    ],
};

export const symptomProgressionData: LineData[] = [
    { name: 'Visit 1', "Pain Score": 8, "Nausea Score": 4 },
    { name: 'Visit 2', "Pain Score": 6, "Nausea Score": 3 },
    { name: 'Visit 3', "Pain Score": 4, "Nausea Score": 1 },
    { name: 'Visit 4', "Pain Score": 2, "Nausea Score": 0 },
];

export const treatmentEfficacyData: MultiBarData[] = [
    { name: 'Week 1', 'Treatment A': 8.2, 'Treatment B': 7.5, 'Placebo': 8.5 },
    { name: 'Week 2', 'Treatment A': 6.5, 'Treatment B': 6.8, 'Placebo': 8.3 },
    { name: 'Week 3', 'Treatment A': 4.1, 'Treatment B': 5.5, 'Placebo': 8.1 },
    { name: 'Week 4', 'Treatment A': 2.5, 'Treatment B': 4.2, 'Placebo': 8.0 },
];

export const diagnosisMismatchData: NameValueData[] = [
    { name: 'Cardiology', value: 12 },
    { name: 'Neurology', value: 8 },
    { name: 'Gastroenterology', value: 15 },
    { name: 'Orthopedics', value: 5 },
];

// 2. Prescribing and Medication
export const topPrescribedMedicationsData: NameValueData[] = [
    { name: 'Lisinopril', value: 450 },
    { name: 'Atorvastatin', value: 420 },
    { name: 'Metformin', value: 380 },
    { name: 'Amoxicillin', value: 350 },
    { name: 'Albuterol', value: 210 },
];

export const antibioticStewardshipData: MultiBarData[] = [
    { name: 'Common Cold', 'Antibiotics': 40, 'No Antibiotics': 60 },
    { name: 'Bronchitis', 'Antibiotics': 65, 'No Antibiotics': 35 },
    { name: 'Bacterial Sinusitis', 'Antibiotics': 85, 'No Antibiotics': 15 },
    { name: 'Strep Throat', 'Antibiotics': 95, 'No Antibiotics': 5 },
];

export const polypharmacyRiskData: NameValueData[] = [
    { name: 'Patients on 5+ Meds', value: 18 },
    { name: 'Patients on <5 Meds', value: 82 },
];

export const prescriptionPatternDeviationsData: MultiBarData[] = [
    { name: 'Dr. Smith', 'Opioids': 15, 'Antibiotics': 30, 'Statins': 40 },
    { name: 'Dr. Jones', 'Opioids': 12, 'Antibiotics': 25, 'Statins': 45 },
    { name: 'Dr. Miller (Outlier)', 'Opioids': 45, 'Antibiotics': 15, 'Statins': 20 },
    { name: 'Dr. Brown', 'Opioids': 10, 'Antibiotics': 35, 'Statins': 50 },
];

export const formularyAdherenceData: NameValueData[] = [
    { name: 'Adherent', value: 92 },
    { name: 'Non-Adherent', value: 8 },
];

// 3. Investigations & Diagnostic
export const topOrderedInvestigationsData: NameValueData[] = [
    { name: 'Complete Blood Count', value: 850 },
    { name: 'Chest X-Ray', value: 620 },
    { name: 'Basic Metabolic Panel', value: 580 },
    { name: 'Urinalysis', value: 410 },
    { name: 'ECG', value: 350 },
];

export const valueTestsData: NameValueData[] = [
    { name: 'High-Value', value: 75 },
    { name: 'Low-Value', value: 25 },
];

export const repeatTestsData: NameValueData[] = [
    { name: 'CBC', value: 45 },
    { name: 'BMP', value: 38 },
    { name: 'Troponin', value: 25 },
    { name: 'PT/INR', value: 15 },
];

export const turnaroundTimeData: MultiBarData[] = [
    { name: 'Lab - STAT', 'Target (min)': 60, 'Actual (min)': 55 },
    { name: 'Lab - Routine', 'Target (min)': 240, 'Actual (min)': 260 },
    { name: 'Imaging - X-Ray', 'Target (min)': 90, 'Actual (min)': 85 },
    { name: 'Imaging - CT', 'Target (min)': 180, 'Actual (min)': 210 },
];

export const investigationAccuracyData: NameValueData[] = [
    { name: 'CT Scan', value: 95 },
    { name: 'MRI', value: 92 },
    { name: 'Ultrasound', value: 85 },
    { name: 'Biopsy', value: 99 },
    { name: 'X-Ray', value: 78 },
];

// 4. Provider Performance
export const providerDiagnosisAccuracyData: NameValueData[] = [
    { name: 'Dr. Green', value: 98 },
    { name: 'Dr. Hall', value: 95 },
    { name: 'Dr. King', value: 92 },
    { name: 'Dr. Lee', value: 88 },
    { name: 'Dept. Average', value: 93 },
];

export const documentationCompletenessData: NameValueData[] = [
    { name: 'Dr. Green', value: 100 },
    { name: 'Dr. Hall', value: 92 },
    { name: 'Dr. King', value: 95 },
    { name: 'Dr. Lee', value: 85 },
];

export const timeToTreatmentData: NameValueData[] = [
    { name: 'Dr. Green', value: 35 },
    { name: 'Dr. Hall', value: 45 },
    { name: 'Dr. King', value: 40 },
    { name: 'Dr. Lee', value: 55 },
    { name: 'Dept. Average (min)', value: 43 },
];

export const patientLoadVsOutcomeData = [
    { cases: 10, outcome: 4.8 },
    { cases: 12, outcome: 4.7 },
    { cases: 15, outcome: 4.5 },
    { cases: 18, outcome: 4.6 },
    { cases: 20, outcome: 4.2 },
    { cases: 22, outcome: 4.1 },
    { cases: 25, outcome: 3.8 },
];

export const clinicalVariationData: MultiBarData[] = [
  { name: 'Dr. Green', 'Test A': 2, 'Test B': 1, 'Med X': 1, 'Med Y': 0},
  { name: 'Dr. Hall', 'Test A': 1, 'Test B': 1, 'Med X': 1, 'Med Y': 1},
  { name: 'Dr. King', 'Test A': 3, 'Test B': 0, 'Med X': 1, 'Med Y': 0},
  { name: 'Dr. Lee', 'Test A': 1, 'Test B': 2, 'Med X': 0, 'Med Y': 1},
];

// 5. Patient Journey
export const patientJourneyData: PatientJourneyEvent[] = [
    { date: 'Jan 15, 2023', type: 'Complaint', details: 'Patient presents with severe, radiating chest pain.', iconPath: "M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" },
    { date: 'Jan 15, 2023', type: 'Investigation', details: 'ECG, Troponin, Chest X-Ray ordered.', iconPath: "M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z" },
    { date: 'Jan 16, 2023', type: 'Diagnosis', details: 'Diagnosed with Unstable Angina.', iconPath: "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" },
    { date: 'Jan 16, 2023', type: 'Treatment', details: 'Started on Aspirin, Nitroglycerin, and Heparin.', iconPath: "M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4H7zm-4-4a2 2 0 104 0 2 2 0 10-4 0z" },
    { date: 'Jan 18, 2023', type: 'Education', details: 'Patient educated on lifestyle modifications.', iconPath: "M10 20l4-16m4 4l-4 4-4-4-4 4" },
];


export const chronicConditionData: LineData[] = [
    { name: 'Q1', 'HbA1c': 8.5, 'BP (Systolic)': 155 },
    { name: 'Q2', 'HbA1c': 8.1, 'BP (Systolic)': 148 },
    { name: 'Q3', 'HbA1c': 7.6, 'BP (Systolic)': 142 },
    { name: 'Q4', 'HbA1c': 7.2, 'BP (Systolic)': 135 },
];

export const treatmentPathwayAdherenceData: NameValueData[] = [
  { name: 'Pathway Adherent', value: 85 },
  { name: 'Deviation', value: 15 },
];

// 6. Operational & Quality
export const incompleteNotesData: NameValueData[] = [
    { name: 'Missing Diagnosis', value: 12 },
    { name: 'Missing Treatment', value: 8 },
    { name: 'Missing Education', value: 25 },
];

export const timeToCloseCaseSheetData: NameValueData[] = [
    { name: 'Cardiology', value: 28 },
    { name: 'Neurology', value: 36 },
    { name: 'General Medicine', value: 22 },
    { name: 'Orthopedics', value: 48 },
    { name: 'Average (hours)', value: 33.5 },
];

export const referralPatternHeatmapData = {
    complaints: ['Headache', 'Chest Pain', 'Dizziness', 'Abdominal Pain'],
    specialties: ['Neurology', 'Cardiology', 'ENT', 'Gastro'],
    data: [
        [90, 5, 20, 2], // Headache
        [10, 85, 5, 15], // Chest Pain
        [40, 30, 60, 5], // Dizziness
        [5, 10, 2, 90],  // Abdominal Pain
    ],
};

// 7. Population Health
export const diseasePrevalenceData: NameValueData[] = [
    { name: 'Hypertension', value: 3500 },
    { name: 'Diabetes', value: 2800 },
    { name: 'Asthma', value: 1500 },
    { name: 'COPD', value: 1200 },
    { name: 'CKD', value: 900 },
];

export const seasonalTrendData: LineData[] = [
    { name: 'Jan', 'Respiratory': 220, 'GI': 80 },
    { name: 'Feb', 'Respiratory': 250, 'GI': 70 },
    { name: 'Mar', 'Respiratory': 180, 'GI': 90 },
    { name: 'Apr', 'Respiratory': 120, 'GI': 150 },
    { name: 'May', 'Respiratory': 90, 'GI': 200 },
    { name: 'Jun', 'Respiratory': 80, 'GI': 280 },
    { name: 'Jul', 'Respiratory': 100, 'GI': 320 },
    { name: 'Aug', 'Respiratory': 150, 'GI': 300 },
    { name: 'Sep', 'Respiratory': 180, 'GI': 250 },
    { name: 'Oct', 'Respiratory': 210, 'GI': 180 },
    { name: 'Nov', 'Respiratory': 280, 'GI': 120 },
    { name: 'Dec', 'Respiratory': 320, 'GI': 100 },
];

export const highRiskPatientData: NameValueData[] = [
    { name: 'Frequent Visits', value: 150 },
    { name: 'Polypharmacy', value: 210 },
    { name: 'Multiple Comorbidities', value: 350 },
];

// 8. Compliance & Quality of Care
export const docCompletenessScoreData: NameValueData[] = [
    { name: 'Complete', value: 88 },
    { name: 'Incomplete', value: 12 },
];

export const prescriptionGuidelineAdherenceData: MultiBarData[] = [
    { name: 'Hypertension', 'Adherent': 95, 'Non-Adherent': 5},
    { name: 'Diabetes', 'Adherent': 92, 'Non-Adherent': 8},
    { name: 'UTI', 'Adherent': 85, 'Non-Adherent': 15},
    { name: 'Pneumonia', 'Adherent': 88, 'Non-Adherent': 12},
];