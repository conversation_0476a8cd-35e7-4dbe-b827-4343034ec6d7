import React from 'react';

interface CardProps {
  title: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
}

const Card: React.FC<CardProps> = ({ title, description, children, className = '' }) => {
  return (
    <div className={`bg-white rounded-lg shadow-md p-6 hover:shadow-xl transition-shadow duration-300 ${className}`}>
      <h3 className="text-xl font-bold text-gray-800 mb-2">{title}</h3>
      {description && <p className="text-gray-600 mb-4 text-sm">{description}</p>}
      <div className="h-72 w-full">{children}</div>
    </div>
  );
};

export default Card;
