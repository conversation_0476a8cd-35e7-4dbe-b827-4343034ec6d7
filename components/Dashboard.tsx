import React from 'react';
import { visits, doctors, chiefComplaintsForAI } from '../mockData';
import { useClinicalData } from '../hooks/useClinicalData';
import StatCard from './StatCard';
import DiagnosisTrendChart from './DiagnosisTrendChart';
import DoctorActivityTable from './DoctorActivityTable';
import DiagnosisByAgeGroupChart from './DiagnosisByAgeGroupChart';
import SymptomClusterer from './SymptomClusterer';
import { ChartIcon, DiagnosisIcon, PillIcon, TestTubeIcon, TrendIcon, UsersIcon, ReferralIcon, AlertTriangleIcon } from './icons/Icons';


const Dashboard: React.FC = () => {
  const { clinicalSummary, diagnosisTrends, doctorActivity, diagnosisByAge, qualityMetrics } = useClinicalData(visits, doctors);

  if (!clinicalSummary || !qualityMetrics) {
    return <div>Loading data...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Section: Clinical Summary */}
      <section>
        <h2 className="text-xl font-semibold text-neutral-dark mb-4">Clinical Summary</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
          <StatCard
            title="Top Complaint"
            value={clinicalSummary.topComplaint.name}
            change={`${clinicalSummary.topComplaint.count} cases`}
            icon={<DiagnosisIcon className="w-6 h-6" />}
            color="blue"
          />
          <StatCard
            title="Top Diagnosis"
            value={clinicalSummary.topDiagnosis.name}
            change={`${clinicalSummary.topDiagnosis.count} cases`}
            icon={<ChartIcon className="w-6 h-6" />}
            color="green"
          />
          <StatCard
            title="Most Prescribed"
            value={clinicalSummary.topMedication.name}
            change={`${clinicalSummary.topMedication.count} times`}
            icon={<PillIcon className="w-6 h-6" />}
            color="indigo"
          />
          <StatCard
            title="Avg. Investigations"
            value={clinicalSummary.avgInvestigations}
            change="per visit"
            icon={<TestTubeIcon className="w-6 h-6" />}
            color="purple"
          />
          <StatCard
            title="Frequent Pathway"
            value={clinicalSummary.mostFrequentPair}
            change="Symptom → Diagnosis"
            icon={<TrendIcon className="w-6 h-6" />}
            color="amber"
          />
        </div>
      </section>

      {/* Section: Operational & Quality Metrics */}
      <section>
        <h2 className="text-xl font-semibold text-neutral-dark mb-4">Operational & Quality Metrics</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <StatCard
                title="Total Patients"
                value={qualityMetrics.totalPatients.toString()}
                change="Unique individuals"
                icon={<UsersIcon className="w-6 h-6" />}
                color="green"
            />
            <StatCard
                title="Referral Rate"
                value={`${qualityMetrics.referralRate.toFixed(1)}%`}
                change="of total visits"
                icon={<ReferralIcon className="w-6 h-6" />}
                color="blue"
            />
            <StatCard
                title="Polypharmacy Alerts"
                value={qualityMetrics.polypharmacyCount.toString()}
                change="Visits w/ 5+ meds"
                icon={<AlertTriangleIcon className="w-6 h-6" />}
                color="amber"
            />
        </div>
      </section>

      {/* Section: Time-based and Demographic Trends */}
      <section className="grid grid-cols-1 lg:grid-cols-5 gap-6">
        <div className="lg:col-span-3 bg-white p-4 sm:p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-neutral-dark mb-4">Monthly Trend - Top 5 Diagnoses</h3>
          <DiagnosisTrendChart data={diagnosisTrends} />
        </div>
        <div className="lg:col-span-2 bg-white p-4 sm:p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-neutral-dark mb-4">Diagnoses by Age Group</h3>
          <DiagnosisByAgeGroupChart data={diagnosisByAge} />
        </div>
      </section>

      {/* Section: Doctor Activity & AI Insights */}
      <section className="grid grid-cols-1 lg:grid-cols-5 gap-6">
        <div className="lg:col-span-3 bg-white p-4 sm:p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-neutral-dark mb-4">Doctor-wise Clinical Activity</h3>
          <DoctorActivityTable data={doctorActivity} />
        </div>
        <div className="lg:col-span-2 bg-white p-4 sm:p-6 rounded-lg shadow">
            <SymptomClusterer complaints={chiefComplaintsForAI} />
        </div>
      </section>
    </div>
  );
};

export default Dashboard;