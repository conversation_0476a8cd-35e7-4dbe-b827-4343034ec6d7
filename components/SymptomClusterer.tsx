
import React, { useState, useCallback } from 'react';
import { SymptomCluster } from '../types';
import { analyzeSymptoms } from '../services/geminiService';
import { BrainIcon } from './icons/BrainIcon';
import { SparklesIcon } from './icons/SparklesIcon';

interface SymptomClustererProps {
  complaints: string[];
}

const LoadingSkeleton = () => (
    <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
            <div key={i} className="animate-pulse flex flex-col space-y-2 p-4 border border-gray-200 rounded-lg">
                <div className="h-4 bg-gray-300 rounded w-1/3"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                <div className="h-3 bg-gray-200 rounded w-3/4"></div>
            </div>
        ))}
    </div>
);


const SymptomClusterer: React.FC<SymptomClustererProps> = ({ complaints }) => {
  const [clusters, setClusters] = useState<SymptomCluster[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleAnalyze = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    setClusters([]);
    try {
      const result = await analyzeSymptoms(complaints);
      setClusters(result);
    } catch (e: any) {
      setError(e.message || 'An unknown error occurred.');
    } finally {
      setIsLoading(false);
    }
  }, [complaints]);

  return (
    <div className="flex flex-col h-full">
      <div className="flex justify-between items-start">
        <h3 className="text-lg font-semibold text-neutral-dark mb-4">AI Symptom Clustering</h3>
        <BrainIcon className="w-8 h-8 text-indigo-500" />
      </div>

      <div className="flex-grow space-y-4">
        {isLoading && <LoadingSkeleton />}
        {error && <div className="text-red-500 bg-red-100 p-3 rounded-lg">{error}</div>}
        {!isLoading && !error && clusters.length > 0 && (
          <div className="space-y-3 overflow-y-auto max-h-[250px] pr-2">
            {clusters.map((cluster, index) => (
              <div key={index} className="bg-brand-light/50 border-l-4 border-brand-secondary p-4 rounded-r-lg">
                <h4 className="font-bold text-brand-dark">{cluster.clusterName}</h4>
                <p className="text-sm text-neutral-dark font-medium mt-1">
                  <span className="font-semibold">Diagnosis:</span> {cluster.probableDiagnosis}
                </p>
                <div className="mt-2 flex flex-wrap gap-1.5">
                  {cluster.symptoms.map((symptom, i) => (
                    <span key={i} className="text-xs bg-white text-brand-primary font-medium px-2 py-1 rounded-full border border-brand-primary/30">
                      {symptom}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="mt-4">
        <button
          onClick={handleAnalyze}
          disabled={isLoading}
          className="w-full bg-brand-primary text-white font-bold py-2 px-4 rounded-lg flex items-center justify-center space-x-2 hover:bg-brand-dark transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>Analyzing...</span>
            </>
          ) : (
            <>
              <SparklesIcon className="w-5 h-5" />
              <span>Analyze Complaints</span>
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default SymptomClusterer;
