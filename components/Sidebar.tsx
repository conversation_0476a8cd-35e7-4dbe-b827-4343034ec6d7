import React from 'react';
import { DASHBOARDS } from '../constants';

interface SidebarProps {
  activeDashboard: string;
  setActiveDashboard: (id: string) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ activeDashboard, setActiveDashboard }) => {
  return (
    <aside className="w-64 bg-dark-sidebar text-white flex flex-col">
      <div className="p-4 border-b border-gray-700 flex items-center">
        <svg className="w-10 h-10 mr-3 text-brand-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M12 6V3m0 18v-3M5.636 5.636L4.222 4.222m15.556 15.556L18.364 18.364M18.364 5.636L19.778 4.222m-15.556 15.556l1.414-1.414M12 12a6 6 0 110-12 6 6 0 010 12z"></path></svg>
        <h1 className="text-xl font-bold">Clinical Intel</h1>
      </div>
      <nav className="flex-grow p-2">
        <ul>
          {DASHBOARDS.map((dashboard) => (
            <li key={dashboard.id}>
              <a
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  setActiveDashboard(dashboard.id);
                }}
                className={`flex items-center px-4 py-3 my-1 rounded-md transition-colors duration-200 ${
                  activeDashboard === dashboard.id
                    ? 'bg-brand-primary text-white'
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                }`}
              >
                {dashboard.icon}
                <span className="text-sm font-medium">{dashboard.name}</span>
              </a>
            </li>
          ))}
        </ul>
      </nav>
      <div className="p-4 border-t border-gray-700 text-center text-xs text-gray-500">
        <p>&copy; 2024 Clinical Intelligence</p>
      </div>
    </aside>
  );
};

export default Sidebar;
