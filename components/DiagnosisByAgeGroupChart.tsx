
import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON>, ResponsiveContainer } from 'recharts';

interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
  }[];
}

interface DiagnosisByAgeGroupChartProps {
  data: ChartData;
}

const colors = ['#3B82F6', '#10B981', '#8B5CF6', '#F59E0B', '#EF4444'];

const DiagnosisByAgeGroupChart: React.FC<DiagnosisByAgeGroupChartProps> = ({ data }) => {
  const chartData = data.labels.map((label, index) => {
    const entry: { name: string; [key: string]: string | number } = { name: label };
    data.datasets.forEach(dataset => {
      entry[dataset.label] = dataset.data[index];
    });
    return entry;
  });

  return (
    <div style={{ width: '100%', height: 300 }}>
      <ResponsiveContainer>
        <BarChart 
            data={chartData}
            margin={{ top: 5, right: 20, left: -10, bottom: 5, }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0"/>
          <XAxis dataKey="name" stroke="#6b7280" fontSize={12} />
          <YAxis stroke="#6b7280" fontSize={12} />
          <Tooltip 
              contentStyle={{ 
                  backgroundColor: '#ffffff', 
                  border: '1px solid #e0e0e0',
                  borderRadius: '0.5rem'
              }}
          />
          <Legend iconSize={10} wrapperStyle={{fontSize: "12px", paddingTop: "10px"}}/>
          {data.datasets.map((dataset, index) => (
            <Bar key={dataset.label} dataKey={dataset.label} fill={colors[index % colors.length]} />
          ))}
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default DiagnosisByAgeGroupChart;
