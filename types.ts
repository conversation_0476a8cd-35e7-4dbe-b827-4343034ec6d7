
// Original dashboard types
export interface Visit {
  id: string;
  patient: {
    id: string;
    age: number;
    gender: 'Male' | 'Female' | 'Other';
  };
  doctor: {
    id: string;
    name: string;
  };
  date: string; // ISO string
  chiefComplaint: string;
  diagnosis: string;
  medications: string[];
  investigations: string[];
  referred: boolean;
}

export interface Doctor {
  id: string;
  name: string;
}

export interface SymptomCluster {
    clusterName: string;
    symptoms: string[];
    probableDiagnosis: string;
}

// Clinical dashboard types
export interface NameValueData {
  name: string;
  value: number;
}

export interface MultiBarData {
  name: string;
  [key: string]: string | number;
}

export interface LineData {
  name: string;
  [key: string]: string | number;
}

export interface SankeyNode {
  name: string;
}

export interface SankeyLink {
  source: number;
  target: number;
  value: number;
}

export interface SankeyData {
  nodes: SankeyNode[];
  links: SankeyLink[];
}

export interface PatientJourneyEvent {
  date: string;
  type: 'Complaint' | 'Investigation' | 'Diagnosis' | 'Treatment' | 'Education';
  details: string;
  iconPath: string;
}
