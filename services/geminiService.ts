
import { GoogleGenAI, Type } from "@google/genai";
import { SymptomCluster } from '../types';

if (!process.env.API_KEY) {
  console.warn("API_KEY environment variable not set. AI features will not work.");
}

const ai = new GoogleGenAI({ apiKey: process.env.API_KEY! });

const responseSchema = {
    type: Type.ARRAY,
    items: {
        type: Type.OBJECT,
        properties: {
            clusterName: {
                type: Type.STRING,
                description: 'A descriptive name for the symptom cluster (e.g., "Respiratory Issues").'
            },
            symptoms: {
                type: Type.ARRAY,
                items: {
                    type: Type.STRING
                },
                description: 'A list of chief complaints that belong to this cluster.'
            },
            probableDiagnosis: {
                type: Type.STRING,
                description: 'The most likely medical diagnosis for this cluster of symptoms.'
            }
        },
        required: ["clusterName", "symptoms", "probableDiagnosis"]
    }
};

export const analyzeSymptoms = async (complaints: string[]): Promise<SymptomCluster[]> => {
    const prompt = `
        You are a clinical data analyst AI. Your task is to analyze a list of patient chief complaints and identify meaningful clusters.
        For each cluster, provide a descriptive name, list the symptoms belonging to it, and suggest the most probable diagnosis.
        Do not create more than 5 clusters. Prioritize common and distinct groupings.
        
        Here is the list of patient complaints:
        ${complaints.join(', ')}
    `;

    try {
        const response = await ai.models.generateContent({
            model: "gemini-2.5-flash",
            contents: prompt,
            config: {
                responseMimeType: "application/json",
                responseSchema: responseSchema,
            },
        });
        
        const jsonText = response.text.trim();
        const parsedJson = JSON.parse(jsonText);
        return parsedJson as SymptomCluster[];

    } catch (error) {
        console.error("Error analyzing symptoms with Gemini API:", error);
        throw new Error("Failed to analyze symptoms. Please check API key and network connection.");
    }
};
